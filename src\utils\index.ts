// Shared utility functions

import { format, formatDistanceToNow, parseISO } from 'date-fns'

import { DATE_FORMATS } from '@/constants'
import type { ComponentSize, ComponentVariant } from '@/types'

// Date formatting utilities
export const formatDate = (date: string | Date, formatType: keyof typeof DATE_FORMATS = 'display'): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  
  switch (formatType) {
    case 'relative':
      return formatDistanceToNow(dateObj, { addSuffix: true })
    case 'display':
      return format(dateObj, 'MMM dd, yyyy')
    case 'input':
      return format(dateObj, 'yyyy-MM-dd')
    case 'timestamp':
      return format(dateObj, 'yyyy-MM-dd HH:mm:ss')
    default:
      return format(dateObj, 'MMM dd, yyyy')
  }
}

// Currency formatting
export const formatCurrency = (amount: number, currency = 'PHP'): string => {
  return new Intl.NumberFormat('en-PH', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
  }).format(amount)
}

// Number formatting
export const formatNumber = (num: number, decimals = 0): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(num)
}

// Fix floating point precision issues for currency values
export const roundToCurrency = (value: number): number => {
  return Math.round(value * 100) / 100
}

// Parse and round currency input to avoid precision issues
export const parseCurrencyInput = (input: string): number => {
  const parsed = parseFloat(input) || 0
  return roundToCurrency(parsed)
}

// String utilities
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

// Array utilities
export const groupBy = <T>(array: T[], key: keyof T): Record<string, T[]> => {
  return array.reduce((groups, item) => {
    const group = String(item[key])
    groups[group] = groups[group] || []
    groups[group].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

export const sortBy = <T>(array: T[], key: keyof T, order: 'asc' | 'desc' = 'asc'): T[] => {
  return [...array].sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]
    
    if (aVal < bVal) return order === 'asc' ? -1 : 1
    if (aVal > bVal) return order === 'asc' ? 1 : -1
    return 0
  })
}

// Object utilities
export const omit = <T extends Record<string, unknown>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> => {
  const result = { ...obj }
  keys.forEach(key => delete result[key])
  return result
}

export const pick = <T extends Record<string, unknown>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> => {
  const result = {} as Pick<T, K>
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}

// Validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isValidPhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^(\+63|0)?[0-9]{10}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

// File utilities
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// CSS class utilities
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ')
}

// Cloudinary URL utilities
export const transformCloudinaryUrl = (url: string, options?: {
  width?: number
  height?: number
  quality?: string | number
  crop?: string
  gravity?: string
}): string => {
  if (!url || !url.includes('cloudinary.com')) {
    return url
  }

  const {
    width = 400,
    height = 400,
    quality = 'auto:best',
    crop = 'fill',
    gravity = 'face'
  } = options || {}

  try {
    // Parse the Cloudinary URL
    const urlParts = url.split('/')
    const uploadIndex = urlParts.findIndex(part => part === 'upload')

    if (uploadIndex === -1) {
      return url
    }

    // Create new transformation parameters for crisp profile pictures
    const transformations = [
      `w_${width}`,
      `h_${height}`,
      `c_${crop}`,
      `g_${gravity}`,
      `q_${quality}`,
      'f_auto',
      'fl_progressive'
    ].join(',')

    // Rebuild URL with new transformations
    const beforeUpload = urlParts.slice(0, uploadIndex + 1)
    const afterUpload = urlParts.slice(uploadIndex + 1)

    // Remove existing transformations (they usually come right after 'upload')
    let resourcePath = afterUpload
    if (afterUpload.length > 0 && afterUpload[0].includes('_')) {
      // Skip existing transformation parameters
      resourcePath = afterUpload.slice(1)
    }

    const newUrl = [
      ...beforeUpload,
      transformations,
      ...resourcePath
    ].join('/')

    return newUrl
  } catch (error) {
    console.warn('Failed to transform Cloudinary URL:', error)
    return url
  }
}

// Enhanced profile picture URL transformer - Facebook-like quality
export const getOptimizedProfilePictureUrl = (url?: string | null): string => {
  if (!url) return ''

  // For debt modal, use higher quality settings
  return transformCloudinaryUrl(url, {
    width: 300,
    height: 300,
    quality: 100,
    crop: 'fill',
    gravity: 'face'
  })
}

// Force clear browser cache for images
export const clearImageCache = () => {
  // Clear Next.js image cache
  if (typeof window !== 'undefined') {
    // Force reload all images by adding timestamp
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      const src = img.src
      if (src && !src.includes('cache_bust=')) {
        const separator = src.includes('?') ? '&' : '?'
        img.src = `${src}${separator}cache_bust=${Date.now()}`
      }
    })
  }
}

export const getVariantClasses = (variant: ComponentVariant): string => {
  const variants = {
    primary: 'bg-green-600 hover:bg-green-700 text-white',
    secondary: 'bg-yellow-500 hover:bg-yellow-600 text-white',
    success: 'bg-emerald-600 hover:bg-emerald-700 text-white',
    warning: 'bg-amber-600 hover:bg-amber-700 text-white',
    error: 'bg-red-600 hover:bg-red-700 text-white',
  }
  return variants[variant]
}

export const getSizeClasses = (size: ComponentSize): string => {
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl',
  }
  return sizes[size]
}

// Local storage utilities
export const storage = {
  get: <T>(key: string, defaultValue?: T): T | null => {
    if (typeof window === 'undefined') return defaultValue || null
    
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue || null
    } catch {
      return defaultValue || null
    }
  },
  
  set: <T>(key: string, value: T): void => {
    if (typeof window === 'undefined') return
    
    try {
      window.localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Failed to save to localStorage:', error)
    }
  },
  
  remove: (key: string): void => {
    if (typeof window === 'undefined') return
    
    try {
      window.localStorage.removeItem(key)
    } catch (error) {
      console.error('Failed to remove from localStorage:', error)
    }
  },
}

// Debounce utility
export const debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Generate random ID
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9)
}
