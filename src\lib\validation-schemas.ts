// =====================================================
// VALIDATION SCHEMAS FOR TINDAHAN STORE
// =====================================================
// Professional Zod validation schemas for API endpoints
// 
// 🎯 PURPOSE: Provide comprehensive input validation and sanitization
// 📅 CREATED: 2025-07-29
// 🔧 USAGE: Import and use in API routes for request validation
// 🛡️ SECURITY: Prevents SQL injection and data corruption
// =====================================================

import { z } from 'zod'

// =====================================================
// COMMON VALIDATION HELPERS
// =====================================================

// Customer name validation (letters, spaces, hyphens, apostrophes, dots)
const customerNameSchema = z
  .string()
  .min(2, 'Name must be at least 2 characters')
  .max(255, 'Name must not exceed 255 characters')
  .regex(/^[A-Za-z\s\-'\.]+$/, 'Name can only contain letters, spaces, hyphens, apostrophes, and dots')
  .transform(val => val.trim())

// Product name validation
const productNameSchema = z
  .string()
  .min(2, 'Product name must be at least 2 characters')
  .max(255, 'Product name must not exceed 255 characters')
  .transform(val => val.trim())

// Date validation (YYYY-MM-DD format)
const dateSchema = z
  .string()
  .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
  .refine(
    (date) => {
      const parsedDate = new Date(date)
      const today = new Date()
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)
      
      return parsedDate >= new Date('2020-01-01') && parsedDate <= tomorrow
    },
    'Date must be between 2020-01-01 and tomorrow'
  )

// Payment method validation
const paymentMethodSchema = z.enum([
  'Cash',
  'GCash', 
  'PayMaya',
  'Bank Transfer',
  'Others'
], {
  errorMap: () => ({ message: 'Invalid payment method' })
})

// Notes validation (optional)
const notesSchema = z
  .string()
  .max(1000, 'Notes must not exceed 1000 characters')
  .transform(val => val.trim())
  .optional()

// =====================================================
// PRODUCT VALIDATION SCHEMAS
// =====================================================

export const CreateProductSchema = z.object({
  name: productNameSchema,
  image_url: z.string().url('Invalid image URL').optional(),
  net_weight: z
    .string()
    .min(1, 'Net weight is required')
    .max(100, 'Net weight must not exceed 100 characters')
    .transform(val => val.trim()),
  price: z
    .number()
    .positive('Price must be positive')
    .max(10000, 'Price must not exceed PHP 10,000')
    .multipleOf(0.01, 'Price must have at most 2 decimal places'),
  stock_quantity: z
    .number()
    .int('Stock quantity must be a whole number')
    .min(0, 'Stock quantity cannot be negative')
    .max(10000, 'Stock quantity must not exceed 10,000'),
  category: z
    .string()
    .min(2, 'Category must be at least 2 characters')
    .max(100, 'Category must not exceed 100 characters')
    .transform(val => val.trim())
})

export const UpdateProductSchema = CreateProductSchema.partial()

// =====================================================
// CUSTOMER VALIDATION SCHEMAS
// =====================================================

export const CreateCustomerSchema = z.object({
  customer_name: customerNameSchema,
  customer_family_name: customerNameSchema,
  profile_picture_url: z.string().url('Invalid profile picture URL').optional(),
  profile_picture_public_id: z.string().max(255).optional(),
  phone_number: z
    .string()
    .regex(/^[0-9+\-\s()]+$/, 'Invalid phone number format')
    .max(20, 'Phone number must not exceed 20 characters')
    .optional(),
  address: z
    .string()
    .max(500, 'Address must not exceed 500 characters')
    .transform(val => val.trim())
    .optional(),
  birth_date: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Birth date must be in YYYY-MM-DD format')
    .refine(
      (date) => {
        const birthDate = new Date(date)
        const today = new Date()
        return birthDate <= today
      },
      'Birth date cannot be in the future'
    )
    .optional(),
  birth_place: z
    .string()
    .min(2, 'Birth place must be at least 2 characters')
    .max(255, 'Birth place must not exceed 255 characters')
    .transform(val => val.trim())
    .optional(),
  notes: notesSchema
})

export const UpdateCustomerSchema = CreateCustomerSchema.partial()

// =====================================================
// CUSTOMER DEBT VALIDATION SCHEMAS
// =====================================================

export const CreateCustomerDebtSchema = z.object({
  customer_name: customerNameSchema,
  customer_family_name: customerNameSchema,
  product_name: productNameSchema,
  product_price: z
    .number()
    .positive('Product price must be positive')
    .max(10000, 'Product price must not exceed PHP 10,000')
    .multipleOf(0.01, 'Product price must have at most 2 decimal places'),
  quantity: z
    .number()
    .int('Quantity must be a whole number')
    .positive('Quantity must be positive')
    .max(1000, 'Quantity must not exceed 1,000'),
  debt_date: dateSchema.optional(),
  notes: notesSchema
})

export const UpdateCustomerDebtSchema = CreateCustomerDebtSchema.partial()

// =====================================================
// CUSTOMER PAYMENT VALIDATION SCHEMAS
// =====================================================

export const CreateCustomerPaymentSchema = z.object({
  customer_name: customerNameSchema,
  customer_family_name: customerNameSchema,
  payment_amount: z
    .number()
    .positive('Payment amount must be positive')
    .max(50000, 'Payment amount must not exceed PHP 50,000')
    .multipleOf(0.01, 'Payment amount must have at most 2 decimal places'),
  payment_date: dateSchema.optional(),
  payment_method: paymentMethodSchema.optional(),
  responsible_family_member: customerNameSchema.optional(),
  notes: notesSchema
})

export const UpdateCustomerPaymentSchema = CreateCustomerPaymentSchema.partial()

// =====================================================
// QUERY PARAMETER VALIDATION SCHEMAS
// =====================================================

export const PaginationSchema = z.object({
  page: z
    .string()
    .regex(/^\d+$/, 'Page must be a positive number')
    .transform(val => Math.max(1, parseInt(val, 10)))
    .default('1'),
  limit: z
    .string()
    .regex(/^\d+$/, 'Limit must be a positive number')
    .transform(val => Math.min(100, Math.max(1, parseInt(val, 10))))
    .default('10')
})

export const SearchSchema = z.object({
  search: z
    .string()
    .max(255, 'Search query must not exceed 255 characters')
    .transform(val => val.trim())
    .optional(),
  customer_name: customerNameSchema.optional(),
  customer_family_name: customerNameSchema.optional(),
  date_from: dateSchema.optional(),
  date_to: dateSchema.optional()
})

export const ProductSearchSchema = SearchSchema.extend({
  category: z
    .string()
    .max(100, 'Category must not exceed 100 characters')
    .transform(val => val.trim())
    .optional(),
  product_name: productNameSchema.optional()
})

export const PaymentSearchSchema = SearchSchema.extend({
  payment_method: paymentMethodSchema.optional()
})

// =====================================================
// VALIDATION HELPER FUNCTIONS
// =====================================================

/**
 * Validates and sanitizes request body using provided schema
 */
export async function validateRequestBody<T>(
  request: Request,
  schema: z.ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json()
    return schema.parse(body)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      throw new Error(`Validation failed: ${errorMessages.join(', ')}`)
    }
    throw new Error('Invalid request body format')
  }
}

/**
 * Validates query parameters using provided schema
 */
export function validateQueryParams<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): T {
  try {
    const params = Object.fromEntries(searchParams.entries())
    return schema.parse(params)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      throw new Error(`Query validation failed: ${errorMessages.join(', ')}`)
    }
    throw new Error('Invalid query parameters')
  }
}

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

/**
 * Validates UUID format
 */
export const UUIDSchema = z
  .string()
  .uuid('Invalid UUID format')

/**
 * Validates and sanitizes file upload data
 */
export const FileUploadSchema = z.object({
  filename: z
    .string()
    .min(1, 'Filename is required')
    .max(255, 'Filename must not exceed 255 characters')
    .regex(/^[a-zA-Z0-9._-]+$/, 'Filename contains invalid characters'),
  mimetype: z
    .string()
    .regex(/^image\/(jpeg|jpg|png|gif|webp)$/, 'Only image files are allowed'),
  size: z
    .number()
    .max(5 * 1024 * 1024, 'File size must not exceed 5MB')
})

// =====================================================
// TYPE EXPORTS
// =====================================================

export type CreateProductInput = z.infer<typeof CreateProductSchema>
export type UpdateProductInput = z.infer<typeof UpdateProductSchema>
export type CreateCustomerInput = z.infer<typeof CreateCustomerSchema>
export type UpdateCustomerInput = z.infer<typeof UpdateCustomerSchema>
export type CreateCustomerDebtInput = z.infer<typeof CreateCustomerDebtSchema>
export type UpdateCustomerDebtInput = z.infer<typeof UpdateCustomerDebtSchema>
export type CreateCustomerPaymentInput = z.infer<typeof CreateCustomerPaymentSchema>
export type UpdateCustomerPaymentInput = z.infer<typeof UpdateCustomerPaymentSchema>
export type PaginationInput = z.infer<typeof PaginationSchema>
export type SearchInput = z.infer<typeof SearchSchema>
export type ProductSearchInput = z.infer<typeof ProductSearchSchema>
export type PaymentSearchInput = z.infer<typeof PaymentSearchSchema>
export type FileUploadInput = z.infer<typeof FileUploadSchema>
