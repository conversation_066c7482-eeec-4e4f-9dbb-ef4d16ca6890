'use client'

import {
  User,
  MapPin,
  Phone,
  Edit3,
  Save,
  X,
  Cake,
  Home
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import { Customer } from '@/lib/supabase'

import ProfilePictureUpload from './ProfilePictureUpload'
import CrispProfilePicture from './CrispProfilePicture'

interface CustomerProfileProps {
  customer: Customer
  onUpdate?: (updatedCustomer: Partial<Customer>) => Promise<void>
  isEditable?: boolean
  compact?: boolean
  isDebtModal?: boolean // New prop to identify debt modal context
}

export default function CustomerProfile({
  customer,
  onUpdate,
  isEditable = false,
  compact = false,
  isDebtModal = false
}: CustomerProfileProps) {
  const { resolvedTheme } = useTheme()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [imageRef<PERSON><PERSON><PERSON>, setImage<PERSON><PERSON>resh<PERSON><PERSON>] = useState(Date.now()) // Add refresh key
  const [formData, setFormData] = useState({
    customer_name: customer.customer_name,
    customer_family_name: customer.customer_family_name,
    phone_number: customer.phone_number || '',
    address: customer.address || '',
    birth_date: customer.birth_date || '',
    birth_place: customer.birth_place || '',
    profile_picture_url: customer.profile_picture_url || '',
    profile_picture_public_id: customer.profile_picture_public_id || '',
    notes: customer.notes || ''
  })

  // Sync formData with customer prop changes (important for when customer data is updated)
  useEffect(() => {
    setFormData({
      customer_name: customer.customer_name,
      customer_family_name: customer.customer_family_name,
      phone_number: customer.phone_number || '',
      address: customer.address || '',
      birth_date: customer.birth_date || '',
      birth_place: customer.birth_place || '',
      profile_picture_url: customer.profile_picture_url || '',
      profile_picture_public_id: customer.profile_picture_public_id || '',
      notes: customer.notes || ''
    })
    setImageRefreshKey(Date.now()) // Force image refresh when customer data changes
  }, [customer])

  const handleSave = async () => {
    if (!onUpdate) return

    setIsLoading(true)
    try {
      await onUpdate(formData)
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update customer:', error)
      alert('Failed to update customer profile')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      customer_name: customer.customer_name,
      customer_family_name: customer.customer_family_name,
      phone_number: customer.phone_number || '',
      address: customer.address || '',
      birth_date: customer.birth_date || '',
      birth_place: customer.birth_place || '',
      profile_picture_url: customer.profile_picture_url || '',
      profile_picture_public_id: customer.profile_picture_public_id || '',
      notes: customer.notes || ''
    })
    setIsEditing(false)
  }

  const handleImageChange = async (imageUrl: string | null, publicId: string | null) => {
    // Store the old public_id for potential cleanup
    const oldPublicId = formData.profile_picture_public_id

    const newFormData = {
      ...formData,
      profile_picture_url: imageUrl || '',
      profile_picture_public_id: publicId || ''
    }

    setFormData(newFormData)
    setImageRefreshKey(Date.now()) // Force refresh of image display

    // Auto-save profile picture in debt modal context
    if (isDebtModal && onUpdate) {
      try {
        const updateData = {
          customer_name: formData.customer_name,
          customer_family_name: formData.customer_family_name,
          profile_picture_url: imageUrl || '',
          profile_picture_public_id: publicId || '',
          phone_number: formData.phone_number || null,
          address: formData.address || null,
          birth_date: formData.birth_date || null,
          birth_place: formData.birth_place || null,
          notes: formData.notes || null
        }

        await onUpdate(updateData)

        // If we successfully saved and there was an old image, delete it from Cloudinary
        if (oldPublicId && imageUrl) { // Only delete if we're replacing with a new image
          try {
            await fetch(`/api/upload/profile-picture?public_id=${encodeURIComponent(oldPublicId)}`, {
              method: 'DELETE'
            })
            console.log(`Successfully deleted old profile picture: ${oldPublicId}`)
          } catch (deleteError) {
            // Don't fail the whole operation if cleanup fails
            console.error('Error deleting old profile picture:', deleteError)
          }
        }
      } catch (error) {
        console.error('Failed to auto-save profile picture:', error)
        // Revert the local state if save failed
        setFormData(prev => ({
          ...prev,
          profile_picture_url: customer.profile_picture_url || '',
          profile_picture_public_id: customer.profile_picture_public_id || ''
        }))
        alert('Failed to save profile picture. Please try again.')
      }
    }
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateAge = (birthDate: string) => {
    if (!birthDate) return null
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  if (compact) {
    return (
      <div className="flex items-center space-x-4">
        {isDebtModal ? (
          <CrispProfilePicture
            imageUrl={customer.profile_picture_url}
            customerName={customer.customer_name}
            customerFamilyName={customer.customer_family_name}
            size="md"
            forceRefresh={true}
          />
        ) : (
          <ProfilePictureUpload
            currentImageUrl={customer.profile_picture_url || ''}
            currentPublicId={customer.profile_picture_public_id || ''}
            onImageChange={() => {}} // Read-only in compact mode
            size="md"
            disabled={true}
            isDebtModal={isDebtModal}
          />
        )}
        <div>
          <h3 className="font-semibold text-lg" style={{
            color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
          }}>
            {customer.customer_name} {customer.customer_family_name}
          </h3>
          {customer.address && (
            <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              {customer.address}
            </p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-lg p-5 shadow-sm border"
      style={{
        borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'
      }}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-5">
        <div className="flex items-center gap-4">
          {isDebtModal && !isEditing ? (
            <CrispProfilePicture
              imageUrl={formData.profile_picture_url}
              customerName={formData.customer_name}
              customerFamilyName={formData.customer_family_name}
              size="xl"
              forceRefresh={true}
              key={imageRefreshKey} // Force re-render when image changes
            />
          ) : (
            <ProfilePictureUpload
              currentImageUrl={formData.profile_picture_url}
              currentPublicId={formData.profile_picture_public_id}
              onImageChange={handleImageChange}
              size="xl"
              disabled={!isEditing}
              isDebtModal={isDebtModal}
            />
          )}
          <div>
            {isEditing ? (
              <div className="space-y-3">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <input
                    type="text"
                    value={formData.customer_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                    className="px-3 py-2 border rounded-lg text-lg font-semibold focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                    }}
                    placeholder="First Name"
                  />
                  <input
                    type="text"
                    value={formData.customer_family_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, customer_family_name: e.target.value }))}
                    className="px-3 py-2 border rounded-lg text-lg font-semibold focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                    }}
                    placeholder="Last Name"
                  />
                </div>
              </div>
            ) : (
              <h2 className="text-2xl font-bold" style={{
                color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
              }}>
                {customer.customer_name} {customer.customer_family_name}
              </h2>
            )}
            
            {customer.birth_date && !isEditing && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Age: {calculateAge(customer.birth_date)} years old
              </p>
            )}
          </div>
        </div>

        {isEditable && (
          <div className="flex items-center gap-3">
            {isEditing ? (
              <>
                <button
                  onClick={handleCancel}
                  disabled={isLoading}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </button>
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <Save className="h-4 w-4" />
                  <span>{isLoading ? 'Saving...' : 'Save'}</span>
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center gap-2 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Edit3 className="h-4 w-4" />
                <span>Edit Profile</span>
              </button>
            )}
          </div>
        )}
      </div>

      {/* Profile Information Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        {/* Contact Information */}
        <div className="space-y-3">
          <h3 className="text-base font-semibold text-gray-900 dark:text-white flex items-center">
            <Phone className="h-4 w-4 mr-2" />
            Contact Information
          </h3>

          <div className="space-y-2.5">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Phone Number
              </label>
              {isEditing ? (
                <input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone_number: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-lg"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                  placeholder="Enter phone number"
                />
              ) : (
                <p className="text-gray-900 dark:text-white">
                  {customer.phone_number || 'Not specified'}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Address
              </label>
              {isEditing ? (
                <textarea
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border rounded-lg resize-none"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                  placeholder="Enter address"
                />
              ) : (
                <p className="text-gray-900 dark:text-white">
                  {customer.address || 'Not specified'}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Personal Information */}
        <div className="space-y-3">
          <h3 className="text-base font-semibold text-gray-900 dark:text-white flex items-center">
            <User className="h-4 w-4 mr-2" />
            Personal Information
          </h3>

          <div className="space-y-2.5">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <Cake className="h-4 w-4 inline mr-1" />
                Birth Date
              </label>
              {isEditing ? (
                <input
                  type="date"
                  value={formData.birth_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, birth_date: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-lg"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                />
              ) : (
                <p className="text-gray-900 dark:text-white">
                  {formatDate(customer.birth_date || '')}
                  {customer.birth_date && (
                    <span className="text-sm text-gray-500 ml-2">
                      ({calculateAge(customer.birth_date)} years old)
                    </span>
                  )}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                <Home className="h-4 w-4 inline mr-1" />
                Birth Place
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.birth_place}
                  onChange={(e) => setFormData(prev => ({ ...prev, birth_place: e.target.value }))}
                  className="w-full px-3 py-2 border rounded-lg"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                  placeholder="Enter birthplace"
                />
              ) : (
                <p className="text-gray-900 dark:text-white">
                  {customer.birth_place || 'Not specified'}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Notes Section */}
      {(isEditing || customer.notes) && (
        <div className="mt-6 pt-6 border-t" style={{
          borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'
        }}>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Notes
          </h3>
          {isEditing ? (
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border rounded-lg resize-none"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
              }}
              placeholder="Add notes about this customer..."
            />
          ) : (
            <p className="text-gray-900 dark:text-white whitespace-pre-wrap">
              {customer.notes || 'No notes available'}
            </p>
          )}
        </div>
      )}
    </div>
  )
}
