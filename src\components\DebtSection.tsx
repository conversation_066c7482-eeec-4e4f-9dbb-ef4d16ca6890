'use client'

import {
  Plus, Trash2, Search, CreditCard, Filter, SortAsc, SortDesc,
  Grid, List, AlertTriangle, Eye,
  ChevronDown, RefreshCw, DollarSign, Users,
  TrendingUp, Receipt
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect, useMemo, useCallback } from 'react'

import { CustomerDebt, CustomerBalance } from '@/lib/supabase'

import CustomerDebtDetailsModal from './CustomerDebtDetailsModal'
import DebtModal from './DebtModal'
import LoadingSkeleton from './LoadingSkeleton'
import PaymentModal from './PaymentModal'

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface DebtSectionProps {}

export default function DebtSection({}: DebtSectionProps) {
  const { resolvedTheme } = useTheme()
  const [balances, setBalances] = useState<CustomerBalance[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // UI State
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [selectedDebts, setSelectedDebts] = useState<Set<string>>(new Set())
  const [isDebtModalOpen, setIsDebtModalOpen] = useState(false)
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)
  const [selectedCustomer, setSelectedCustomer] = useState<{ name: string; familyName: string } | null>(null)

  // Sukli confirmation dialog state
  const [sukliDialog, setSukliDialog] = useState<{
    isOpen: boolean
    customerName: string
    customerFamilyName: string
    amount: number
  }>({
    isOpen: false,
    customerName: '',
    customerFamilyName: '',
    amount: 0
  })

  // Filters
  const [filters, setFilters] = useState({
    hasBalance: false,
    dateFrom: '',
    dateTo: '',
    paymentMethod: '',
    sortBy: 'debt_date',
    sortOrder: 'desc' as 'asc' | 'desc'
  })

  // Fetch data



  const fetchBalances = useCallback(async () => {
    try {
      const response = await fetch('/api/customer-balances?limit=1000')

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to fetch balances: ${response.status} ${errorText}`)
      }

      const data = await response.json()

      // Extract balances from the API response structure
      const balances = data.data?.balances || data.balances || []
      setBalances(balances)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch balances')
    }
  }, [])

  const fetchAllData = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      await fetchBalances()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }, [fetchBalances])



  useEffect(() => {
    fetchAllData()
  }, [fetchAllData])

  // Filter and sort balances
  const filteredAndSortedBalances = useMemo(() => {
    const filtered = balances.filter(balance => {
      const matchesSearch = searchTerm === '' ||
        balance.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        balance.customer_family_name.toLowerCase().includes(searchTerm.toLowerCase())

      // Include customers with remaining balance OR overpayments (change/sukli)
      const actualRemaining = balance.total_debt - balance.total_payments
      const hasRemainingBalance = actualRemaining > 0
      const hasOverpayment = actualRemaining < 0
      const matchesBalance = !filters.hasBalance || hasRemainingBalance || hasOverpayment

      // Date filtering (only apply if dates are set)
      const matchesDateFrom = !filters.dateFrom ||
        (balance.last_debt_date && balance.last_debt_date >= filters.dateFrom) ||
        (balance.last_payment_date && balance.last_payment_date >= filters.dateFrom)

      const matchesDateTo = !filters.dateTo ||
        (balance.last_debt_date && balance.last_debt_date <= filters.dateTo) ||
        (balance.last_payment_date && balance.last_payment_date <= filters.dateTo)

      return matchesSearch && matchesBalance && matchesDateFrom && matchesDateTo
    })

    // Sort balances
    filtered.sort((a, b) => {
      let aValue: string | number | null | undefined = a[filters.sortBy as keyof CustomerBalance]
      let bValue: string | number | null | undefined = b[filters.sortBy as keyof CustomerBalance]

      // Handle null/undefined values - treat them as empty strings for sorting
      if (aValue == null) aValue = ''
      if (bValue == null) bValue = ''

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      // Ensure we have comparable values
      const compareA = aValue ?? 0
      const compareB = bValue ?? 0

      if (filters.sortOrder === 'asc') {
        return compareA < compareB ? -1 : compareA > compareB ? 1 : 0
      } else {
        return compareA > compareB ? -1 : compareA < compareB ? 1 : 0
      }
    })

    return filtered
  }, [balances, searchTerm, filters])

  // Handle debt operations



  const handleAddPayment = (customerName: string, familyName: string) => {
    setSelectedCustomer({ name: customerName, familyName })
    setIsPaymentModalOpen(true)
  }

  // Handle sukli click
  const handleSukliClick = (customerName: string, customerFamilyName: string, amount: number) => {
    setSukliDialog({
      isOpen: true,
      customerName,
      customerFamilyName,
      amount
    })
  }

  // Handle sukli confirmation
  const handleSukliConfirmation = async (isGiven: boolean) => {
    if (isGiven) {
      // Record sukli as a debt adjustment to properly balance the overpayment
      try {
        const response = await fetch('/api/debts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            customer_name: sukliDialog.customerName,
            customer_family_name: sukliDialog.customerFamilyName,
            product_name: 'SUKLI_ADJUSTMENT',
            product_price: sukliDialog.amount,
            quantity: 1,
            debt_date: new Date().toISOString().split('T')[0],
            notes: `Sukli adjustment - ₱${sukliDialog.amount.toFixed(2)} change given to customer`
          }),
        })

        if (!response.ok) {
          throw new Error('Failed to record sukli adjustment')
        }

        await fetchAllData() // Refresh the data
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to record sukli adjustment')
      }
    }

    // Close dialog
    setSukliDialog({
      isOpen: false,
      customerName: '',
      customerFamilyName: '',
      amount: 0
    })
  }

  const handleAddDebt = (customerName: string, familyName: string) => {
    setSelectedCustomer({ name: customerName, familyName })
    setEditingDebt(null)
    setIsDebtModalOpen(true)
  }

  const handleViewDetails = (customerName: string, familyName: string) => {
    setSelectedCustomer({ name: customerName, familyName })
    setIsDetailsModalOpen(true)
  }

  const handleBulkDelete = async () => {
    if (selectedDebts.size === 0) return
    if (!confirm(`Are you sure you want to delete ${selectedDebts.size} debt record(s)?`)) return

    try {
      await Promise.all(
        Array.from(selectedDebts).map(debtId =>
          fetch(`/api/debts/${debtId}`, { method: 'DELETE' })
        )
      )
      setSelectedDebts(new Set())
      await fetchAllData()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete debts')
    }
  }





  // Calculate summary statistics with proper overpayment handling
  const summaryStats = useMemo(() => {
    const totalDebt = balances.reduce((sum, balance) => sum + balance.total_debt, 0)
    const totalPayments = balances.reduce((sum, balance) => sum + balance.total_payments, 0)

    // Fix: Calculate remaining balance properly (never negative)
    const remainingBalance = balances.reduce((sum, balance) => {
      const actualRemaining = balance.total_debt - balance.total_payments
      return sum + Math.max(actualRemaining, 0)
    }, 0)

    // Calculate total change/sukli for overpayments
    const totalChange = balances.reduce((sum, balance) => {
      const overpayment = balance.total_payments - balance.total_debt
      return sum + Math.max(overpayment, 0)
    }, 0)

    const customersWithDebt = balances.filter(balance => {
      const actualRemaining = balance.total_debt - balance.total_payments
      return actualRemaining > 0
    }).length

    return {
      totalDebt,
      totalPayments,
      remainingBalance,
      totalChange,
      customersWithDebt,
      totalCustomers: balances.length
    }
  }, [balances])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
        
        <LoadingSkeleton type="products" count={6} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <h3
          className="text-lg font-semibold mb-2"
          style={{
            color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
          }}
        >
          Error Loading Debt Data
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <button
          onClick={fetchAllData}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header with Summary Stats */}
      <div className="space-y-3">
        {/* Summary Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          <div
            className="p-3 rounded-lg border shadow-sm"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Total Debt</p>
                <p className="text-lg font-bold text-red-600 dark:text-red-400">
                  ₱{summaryStats.totalDebt.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                </p>
              </div>
              <CreditCard className="h-6 w-6 text-red-500" />
            </div>
          </div>

          <div
            className="p-3 rounded-lg border shadow-sm"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Total Payments</p>
                <p className="text-lg font-bold text-green-600 dark:text-green-400">
                  ₱{summaryStats.totalPayments.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                </p>
              </div>
              <DollarSign className="h-6 w-6 text-green-500" />
            </div>
          </div>

          <div
            className="p-3 rounded-lg border shadow-sm"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Remaining Balance</p>
                <p className="text-lg font-bold text-orange-600 dark:text-orange-400">
                  ₱{summaryStats.remainingBalance.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                </p>
              </div>
              <TrendingUp className="h-6 w-6 text-orange-500" />
            </div>
          </div>

          {/* New: Total Change/Sukli Card */}
          {summaryStats.totalChange > 0 && (
            <div
              className="p-3 rounded-lg border shadow-sm"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Total Sukli</p>
                  <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                    ₱{summaryStats.totalChange.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                  </p>
                </div>
                <DollarSign className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          )}

          <div
            className="p-3 rounded-lg border shadow-sm"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Customers with Debt</p>
                <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                  {summaryStats.customersWithDebt}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  of {summaryStats.totalCustomers} total
                </p>
              </div>
              <Users className="h-6 w-6 text-blue-500" />
            </div>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-3">
          {/* Search Section */}
          <div className="flex flex-col sm:flex-row gap-2 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                }}
              />
              <input
                type="text"
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-lg border transition-all duration-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              />
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center px-4 py-2.5 rounded-xl border transition-all duration-300 hover:scale-[1.02] ${
                showFilters
                  ? 'bg-green-100 border-green-300 text-green-700 dark:bg-green-900 dark:border-green-700 dark:text-green-300'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              style={{
                backgroundColor: showFilters
                  ? undefined
                  : (resolvedTheme === 'dark' ? '#374151' : '#ffffff'),
                border: showFilters
                  ? undefined
                  : (resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'),
                color: showFilters
                  ? undefined
                  : (resolvedTheme === 'dark' ? '#f9fafb' : '#111827')
              }}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              <ChevronDown className={`h-4 w-4 ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3">
            {selectedDebts.size > 0 && (
              <button
                onClick={handleBulkDelete}
                className="flex items-center px-4 py-2.5 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete ({selectedDebts.size})
              </button>
            )}

            <button
              onClick={() => {
                setEditingDebt(null)
                setIsDebtModalOpen(true)
              }}
              className="flex items-center px-6 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Debt
            </button>
          </div>
        </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div
            className="p-4 rounded-lg border shadow-sm animate-slide-down"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f8fafc',
              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e2e8f0'
            }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Show Only
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.hasBalance}
                    onChange={(e) => setFilters(prev => ({ ...prev, hasBalance: e.target.checked }))}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm" style={{
                    color: resolvedTheme === 'dark' ? '#d1d5db' : '#6b7280'
                  }}>
                    Customers with balance or overpayment
                  </span>
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Date From
                </label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                  className="w-full px-3 py-2 rounded-lg border"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#1f2937' : '#ffffff',
                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Date To
                </label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                  className="w-full px-3 py-2 rounded-lg border"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#1f2937' : '#ffffff',
                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Sort By
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                  className="w-full px-3 py-2 rounded-lg border"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#1f2937' : '#ffffff',
                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                >
                  <option value="remaining_balance">Remaining Balance</option>
                  <option value="total_debt">Total Debt</option>
                  <option value="customer_name">Customer Name</option>
                  <option value="last_debt_date">Last Debt Date</option>
                </select>
              </div>
            </div>

            <div className="flex justify-between items-center mt-4">
              <button
                onClick={() => setFilters({
                  hasBalance: false,
                  dateFrom: '',
                  dateTo: '',
                  paymentMethod: '',
                  sortBy: 'remaining_balance',
                  sortOrder: 'desc'
                })}
                className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                Clear Filters
              </button>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => setFilters(prev => ({
                    ...prev,
                    sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc'
                  }))}
                  className="flex items-center px-3 py-1.5 text-sm rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-700"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#1f2937' : '#ffffff',
                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                >
                  {filters.sortOrder === 'asc' ? (
                    <SortAsc className="h-4 w-4 mr-1" />
                  ) : (
                    <SortDesc className="h-4 w-4 mr-1" />
                  )}
                  {filters.sortOrder === 'asc' ? 'Ascending' : 'Descending'}
                </button>

                <div className="flex rounded-lg border" style={{
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'
                }}>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                    title="Grid View"
                  >
                    <Grid className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 transition-colors ${
                      viewMode === 'list'
                        ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                    title="List View"
                  >
                    <List className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Customer Balances Grid/List */}
      {filteredAndSortedBalances.length === 0 ? (
        <div className="text-center py-12">
          <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3
            className="text-lg font-semibold mb-2"
            style={{
              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
            }}
          >
            {searchTerm || filters.hasBalance ? 'No customers found' : 'No debt records yet'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {searchTerm || filters.hasBalance
              ? 'Try adjusting your search terms or filter criteria'
              : 'Start by adding your first customer debt record to track payments and balances'}
          </p>
          {!searchTerm && !filters.hasBalance && (
            <button
              onClick={() => {
                setEditingDebt(null)
                setIsDebtModalOpen(true)
              }}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add First Debt Record
            </button>
          )}
        </div>
      ) : (
        <div className={viewMode === 'grid'
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
          : "space-y-3"
        }>
          {filteredAndSortedBalances.map((balance) => (
            <div
              key={`${balance.customer_name}-${balance.customer_family_name}`}
              className={`relative group rounded-lg shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md hover:scale-[1.01] ${
                viewMode === 'list' ? 'flex items-center' : ''
              }`}
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
              }}
            >
              <div className={`p-4 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                {/* Customer Info */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1 min-w-0 pr-3">
                    <h3
                      className="text-base font-semibold"
                      style={{
                        color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                      }}
                    >
                      {balance.customer_name} {balance.customer_family_name}
                    </h3>
                    <div className="flex items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                      <span className="flex items-center">
                        <Receipt className="h-4 w-4 mr-1" />
                        {balance.debt_count} debt{balance.debt_count !== 1 ? 's' : ''}
                      </span>
                      <span className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        {balance.payment_count} payment{balance.payment_count !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 flex-shrink-0">
                    <button
                      onClick={() => handleViewDetails(balance.customer_name, balance.customer_family_name)}
                      className="p-2 rounded-lg bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 transition-colors"
                      title="View Details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleAddDebt(balance.customer_name, balance.customer_family_name)}
                      className="p-2 rounded-lg bg-orange-100 text-orange-700 hover:bg-orange-200 dark:bg-orange-900 dark:text-orange-300 dark:hover:bg-orange-800 transition-colors"
                      title="Add Debt"
                    >
                      <CreditCard className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleAddPayment(balance.customer_name, balance.customer_family_name)}
                      className="p-2 rounded-lg bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 transition-colors"
                      title="Add Payment"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Balance Information */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Total Debt:</span>
                    <span className="font-semibold text-red-600 dark:text-red-400 text-sm">
                      ₱{balance.total_debt.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Total Payments:</span>
                    <span className="font-semibold text-green-600 dark:text-green-400 text-sm">
                      ₱{balance.total_payments.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                    </span>
                  </div>

                  {/* Fixed: Proper overpayment handling */}
                  {(() => {
                    const actualRemaining = balance.total_debt - balance.total_payments
                    const isOverpaid = actualRemaining < 0
                    const remainingBalance = Math.max(actualRemaining, 0)
                    const changeAmount = Math.max(-actualRemaining, 0)

                    return (
                      <>
                        <div className="flex justify-between items-center pt-1 border-t border-gray-200 dark:border-gray-600">
                          <span
                            className="text-xs font-medium"
                            style={{
                              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                            }}
                          >
                            Remaining Balance:
                          </span>
                          <span className={`font-bold text-base ${
                            remainingBalance > 0
                              ? 'text-orange-600 dark:text-orange-400'
                              : 'text-green-600 dark:text-green-400'
                          }`}>
                            ₱{remainingBalance.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                          </span>
                        </div>

                        {/* Show change/sukli for overpayments */}
                        {isOverpaid && changeAmount > 0 && (
                          <div className="flex justify-between items-center pt-1">
                            <span
                              className="text-xs font-medium"
                              style={{
                                color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                              }}
                            >
                              Sukli (Change):
                            </span>
                            <button
                              onClick={() => handleSukliClick(balance.customer_name, balance.customer_family_name, changeAmount)}
                              className="relative group font-bold text-base text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-300 cursor-pointer"
                              title="Click to mark sukli as given"
                            >
                              {/* Rotating border effect */}
                              <div className="absolute inset-0 rounded-lg border-2 border-blue-400 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-opacity duration-300"></div>
                              <div className="absolute inset-0 rounded-lg border border-blue-300 opacity-0 group-hover:opacity-50 group-hover:animate-spin transition-opacity duration-300" style={{ animationDuration: '3s' }}></div>

                              {/* Content */}
                              <span className="relative z-10 px-2 py-1 rounded-lg group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20 transition-colors duration-300">
                                ₱{changeAmount.toLocaleString('en-PH', { minimumFractionDigits: 2 })}
                              </span>
                            </button>
                          </div>
                        )}
                      </>
                    )
                  })()}
                </div>

                {/* Last Activity */}
                {(balance.last_debt_date || balance.last_payment_date) && (
                  <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                      {balance.last_debt_date && (
                        <span>Last debt: {new Date(balance.last_debt_date).toLocaleDateString()}</span>
                      )}
                      {balance.last_payment_date && (
                        <span>Last payment: {new Date(balance.last_payment_date).toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modals */}
      <DebtModal
        isOpen={isDebtModalOpen}
        onClose={() => {
          setIsDebtModalOpen(false)
          setEditingDebt(null)
          setSelectedCustomer(null)
        }}
        onSave={fetchAllData}
        debt={editingDebt}
        customerName={selectedCustomer?.name || ''}
        customerFamilyName={selectedCustomer?.familyName || ''}
      />

      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => {
          setIsPaymentModalOpen(false)
          setSelectedCustomer(null)
        }}
        onSave={fetchAllData}
        customerName={selectedCustomer?.name || ''}
        customerFamilyName={selectedCustomer?.familyName || ''}
      />

      <CustomerDebtDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false)
          setSelectedCustomer(null)
        }}
        customerName={selectedCustomer?.name || ''}
        customerFamilyName={selectedCustomer?.familyName || ''}
        onRefresh={fetchAllData}
      />

      {/* Sukli Confirmation Dialog */}
      {sukliDialog.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1f2937' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #374151' : '1px solid #e5e7eb'
            }}
          >
            <div className="text-center">
              {/* Icon */}
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 mb-4">
                <DollarSign className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>

              {/* Title */}
              <h3
                className="text-lg font-semibold mb-2"
                style={{
                  color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                }}
              >
                Sukli Confirmation
              </h3>

              {/* Message */}
              <p
                className="text-sm mb-6"
                style={{
                  color: resolvedTheme === 'dark' ? '#d1d5db' : '#6b7280'
                }}
              >
                Nabigay na ba ang sukli na{' '}
                <span className="font-bold text-blue-600 dark:text-blue-400">
                  ₱{sukliDialog.amount.toFixed(2)}
                </span>{' '}
                kay{' '}
                <span className="font-semibold">
                  {sukliDialog.customerName} {sukliDialog.customerFamilyName}
                </span>
                ?
              </p>

              {/* Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={() => handleSukliConfirmation(false)}
                  className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  Hindi pa
                </button>
                <button
                  onClick={() => handleSukliConfirmation(true)}
                  className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                >
                  Oo, nabigay na
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
